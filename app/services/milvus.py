"""
Milvus Lite service for vector database operations.
Handles movie embeddings for recommendation system using a single collection.
"""
import logging
import numpy as np
from typing import Dict, List, Optional, Set, Any, Union
import time
import gc
import os
from pathlib import Path
from pymilvus import MilvusClient, DataType, CollectionSchema, FieldSchema
from ..core.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

class MilvusService:
    """Service for managing vector operations with Milvus Lite using pymilvus.MilvusClient."""

    def __init__(self):
        """Initialize the Milvus Lite service."""
        self.client = None
        self.initialized = False
        self.collection_name = "movie_embeddings"
        self.embedding_dim = settings.EMBEDDING_DIMENSION

        # Use a proper data directory for Milvus (volume location)
        self.data_dir = Path("data/milvus")
        self.db_file = self.data_dir / "milvus_lite.db"

        # Try different URI formats to avoid socket issues
        self.uri_options = [
            f"file://{self.db_file.absolute()}",  # File URI format
            str(self.db_file.absolute()),         # Absolute path
            f"{self.db_file.absolute()}",         # Direct path
        ]

        # Maximum number of retries for initialization
        self.max_retries = 3
        self.retry_delay = 5  # seconds

    def initialize(self) -> None:
        """Initialize the Milvus Lite client and create collections if they don't exist."""
        if self.initialized:
            return

        retry_count = 0
        while retry_count < self.max_retries:
            try:
                self.data_dir.mkdir(parents=True, exist_ok=True)

                if not os.access(self.data_dir, os.W_OK):
                    raise PermissionError(f"No write permission for directory: {self.data_dir}")

                # Try different URI formats to avoid socket issues
                client_created = False
                last_error = None

                for uri_option in self.uri_options:
                    try:
                        logger.info(f"Attempting to connect to Milvus Lite with URI: {uri_option}")

                        # Set environment variables to force file mode
                        os.environ['MILVUS_LITE_DISABLE_SOCKET'] = 'true'
                        os.environ['MILVUS_LITE_FORCE_FILE_MODE'] = 'true'

                        self.client = MilvusClient(uri=uri_option)

                        # Test the connection by listing collections
                        collections = self.client.list_collections()
                        logger.info(f"Successfully connected with URI: {uri_option}")
                        client_created = True
                        break

                    except Exception as uri_error:
                        logger.warning(f"Failed to connect with URI {uri_option}: {str(uri_error)}")
                        last_error = uri_error
                        continue

                if not client_created:
                    raise RuntimeError(f"Failed to connect with any URI format. Last error: {last_error}")

                collections = self.client.list_collections()

                if self.collection_name in collections:
                    if self.check_collections():
                        self.initialized = True
                        return
                    self.drop_collections()
                    time.sleep(1)

                self._create_collections()
                time.sleep(1)

                if not self._verify_collection():
                    raise RuntimeError("Failed to verify collection after creation")

                self.initialized = True
                return

            except Exception as e:
                retry_count += 1
                if retry_count == self.max_retries:
                    logger.error(f"Failed to initialize Milvus Lite: {str(e)}")
                    raise RuntimeError(f"Failed to initialize Milvus Lite: {str(e)}")
                time.sleep(self.retry_delay)

    def _verify_collection(self) -> bool:
        """Verify that the collection exists and has the correct dimension."""
        try:
            collections = self.client.list_collections()
            if self.collection_name not in collections:
                return False

            collection_info = self.client.describe_collection(self.collection_name)

            if not isinstance(collection_info, dict) or 'fields' not in collection_info:
                return False

            for field in collection_info['fields']:
                if field['name'] == 'vector' and 'params' in field and 'dim' in field['params']:
                    return field['params']['dim'] == self.embedding_dim

            return False

        except Exception:
            return False

    def _create_collections(self) -> None:
        """Create a unified collection for all movie data and embeddings."""
        logger.info(f"Creating collection: {self.collection_name} with dimension {self.embedding_dim}")
            
        # Define fields
        fields = [
            FieldSchema(name="id", dtype=DataType.INT64, is_primary=True),
            FieldSchema(name="imdb_id", dtype=DataType.VARCHAR, max_length=20),
            FieldSchema(name="title", dtype=DataType.VARCHAR, max_length=500),
            FieldSchema(name="overview", dtype=DataType.VARCHAR, max_length=10000),
            FieldSchema(name="tagline", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="release_date", dtype=DataType.VARCHAR, max_length=20),
            FieldSchema(name="revenue", dtype=DataType.FLOAT),
            FieldSchema(name="status", dtype=DataType.VARCHAR, max_length=50),
            FieldSchema(name="imdb_rating", dtype=DataType.FLOAT),
            FieldSchema(name="vote_average", dtype=DataType.FLOAT),
            FieldSchema(name="poster_path", dtype=DataType.VARCHAR, max_length=200),
            FieldSchema(name="genres", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="production_countries", dtype=DataType.VARCHAR, max_length=1000),
            FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.embedding_dim)
        ]

        schema = CollectionSchema(fields=fields, description="Movie embeddings collection")
        self.client.create_collection(collection_name=self.collection_name, schema=schema)

        # Create index on the vector field for similarity search
        self._create_vector_index()

    def _create_vector_index(self) -> None:
        """Create an index on the vector field for efficient similarity search."""
        try:
            logger.info("Creating vector index for similarity search...")

            # Prepare index parameters using the MilvusClient method
            index_params = self.client.prepare_index_params()

            # Add index for the vector field using AUTOINDEX algorithm
            # Note: Milvus Lite supports FLAT, IVF_FLAT, and AUTOINDEX
            # AUTOINDEX is the simplest and most reliable option for Milvus Lite
            index_params.add_index(
                field_name="vector",
                index_type="AUTOINDEX",
                metric_type="COSINE",  # Use cosine similarity
                index_name="vector_index"
            )

            # Create the index
            self.client.create_index(
                collection_name=self.collection_name,
                index_params=index_params,
                sync=True  # Wait for index creation to complete
            )

            logger.info("Successfully created vector index")

        except Exception as e:
            logger.error(f"Error creating vector index: {str(e)}")
            raise RuntimeError(f"Failed to create vector index: {str(e)}")

    def _check_vector_index_exists(self) -> bool:
        try:
            index_info = self.client.describe_index(
                collection_name=self.collection_name, 
                index_name="vector_index"
            )
            return index_info.get('field_name') == 'vector'
        except Exception:
            return False

    def _check_initialized(self) -> bool:
        """Check if the service is initialized."""
        if not self.initialized:
            logger.warning("Milvus Lite service is not initialized")
            return False
        return True

    def check_collections(self) -> bool:
        """Check if the collection exists, has data, and has a vector index."""
        # Allow checking collections even during initialization if client exists
        if not self.client:
            if not self.initialized:
                logger.debug("Milvus client not yet available during initialization")
            else:
                logger.warning("Milvus Lite service is not initialized")
            return False

        try:
            collections = self.client.list_collections()
            if self.collection_name not in collections:
                logger.info(f"Collection {self.collection_name} does not exist")
                return False

            # Check if vector index exists
            if not self._check_vector_index_exists():
                logger.info(f"Collection {self.collection_name} exists but vector index is missing")
                return False

            # Check if collection has any data by querying with limit 1
            try:
                query_result = self.client.query(
                    collection_name=self.collection_name,
                    filter="id > 0",
                    output_fields=["id"],
                    limit=1
                )

                logger.info(f"Collection {self.collection_name} has data: {len(query_result)} and vector index exists")
                return len(query_result) > 0
            except Exception as query_error:
                logger.warning(f"Error querying collection: {str(query_error)}")
                # If we can't query the collection, assume it's not ready
                return False

        except Exception as e:
            logger.error(f"Error checking collection existence: {str(e)}")
            return False

    def get_collection_dimension(self) -> Optional[int]:
        """Get the dimension of the vector field in the collection."""
        if not self._check_initialized():
            return None

        try:
            if not self.check_collections():
                logger.warning(f"Collection {self.collection_name} does not exist")
                return None

            collection_info = self.client.describe_collection(self.collection_name)
            return collection_info.get('dimension', None)

        except Exception as e:
            logger.error(f"Error getting collection dimension: {str(e)}")
            return None

    def _prepare_embedding(self, embedding: Union[np.ndarray, Dict, List]) -> List[float]:
        """Convert embedding to the format expected by Milvus Lite."""
        try:
            logger.debug(f"Input embedding type: {type(embedding)}")
            logger.debug(f"Input embedding content: {embedding}")

            # Handle dictionary input
            if isinstance(embedding, dict):
                # Try to extract vector data from common keys
                if 'vector' in embedding:
                    embedding = embedding['vector']
                elif 'embedding' in embedding:
                    embedding = embedding['embedding']
                elif 'data' in embedding:
                    embedding = embedding['data']
                else:
                    # Try to get the first value if it's a single-key dictionary
                    values = list(embedding.values())
                    if values:
                        embedding = values[0]
                    else:
                        raise ValueError("Dictionary does not contain vector data")

            # Convert to numpy array if it's not already
            if not isinstance(embedding, np.ndarray):
                try:
                    embedding = np.array(embedding, dtype=np.float32)
                except Exception as e:
                    logger.error(f"Failed to convert to numpy array: {str(e)}")
                    logger.error(f"Input type: {type(embedding)}")
                    logger.error(f"Input content: {embedding}")
                    raise

            # Handle different array shapes
            if embedding.ndim == 0:
                # Scalar value
                embedding = np.array([embedding], dtype=np.float32)
            elif embedding.ndim > 1:
                # Flatten multi-dimensional array
                embedding = embedding.flatten()

            # Ensure the embedding has the correct dimension
            if len(embedding) != self.embedding_dim:
                raise ValueError(f"Embedding dimension mismatch. Expected {self.embedding_dim}, got {len(embedding)}")

            return embedding.astype(np.float32).tolist()

        except Exception as e:
            logger.error(f"Error preparing embedding: {str(e)}", exc_info=True)
            raise ValueError(f"Failed to prepare embedding: {str(e)}")

    def insert_overview_embeddings(self, overview_embeddings: Dict[str, np.ndarray]) -> bool:
        """Insert movie data and embeddings into Milvus Lite."""
        if not self._check_initialized():
            return False

        try:
            logger.info(f"Starting to insert movie data and embeddings into Milvus Lite. Total embeddings: {len(overview_embeddings)}")

            # Process in batches
            batch_size = 10000
            total_embeddings = len(overview_embeddings)
            processed = 0
            failed = 0

            # Import movie service here to avoid circular imports
            from ..services.movie import movie_service

            # Verify that movies_df is not None and has data
            if movie_service.movies_df is None or movie_service.movies_df.empty:
                logger.error("Movie DataFrame is None or empty")
                return False

            logger.info(f"Movie DataFrame shape: {movie_service.movies_df.shape}")

            for i in range(0, total_embeddings, batch_size):
                batch_end = min(i + batch_size, total_embeddings)
                batch_items = list(overview_embeddings.items())[i:batch_end]
                logger.info(f"Processing batch {i//batch_size + 1} with {len(batch_items)} items")

                # Prepare batch data
                batch_data = []

                for imdb_id, embedding in batch_items:
                    try:
                        logger.debug(f"Processing movie {imdb_id}")
                        logger.debug(f"Embedding type: {type(embedding)}")
                        logger.debug(f"Embedding shape: {getattr(embedding, 'shape', 'no shape')}")

                        # Convert embedding to list of floats
                        embedding_list = self._prepare_embedding(embedding)
                        logger.debug(f"Embedding prepared, length: {len(embedding_list)}")

                        # Skip if dimension doesn't match
                        if len(embedding_list) != self.embedding_dim:
                            logger.warning(f"Skipping movie {imdb_id} with wrong dimension: {len(embedding_list)} != {self.embedding_dim}")
                            continue

                        # Generate a numeric ID from the imdb_id string
                        numeric_id = abs(hash(imdb_id)) % (2**63 - 1)  # Ensure it fits in int64

                        # Get the movie data - using safer DataFrame access
                        movie_mask = movie_service.movies_df['imdb_id'] == imdb_id
                        matching_movies = movie_service.movies_df[movie_mask]

                        if matching_movies.empty:
                            logger.warning(f"No movie data found for imdb_id: {imdb_id}")
                            continue

                        # Get the first matching movie
                        movie_data = matching_movies.iloc[0].to_dict()
                        logger.debug(f"Extracted movie data for {imdb_id}")

                        # Convert lists to JSON strings
                        genres_json = str(movie_data.get('genres', '[]'))
                        countries_json = str(movie_data.get('production_countries', '[]'))

                        # Get numeric values with defaults
                        imdb_rating = float(movie_data.get('imdb_rating', 0.0))
                        vote_average = float(movie_data.get('vote_average', 0.0))
                        revenue = float(movie_data.get('revenue', 0.0)) if 'revenue' in movie_data else 0.0

                        # Add to batch data
                        movie_entry = {
                            "id": numeric_id,  # Use a consistent ID generation method
                            "imdb_id": imdb_id,
                            "title": str(movie_data.get('title', ''))[:499],  # Truncate to fit field size
                            "overview": str(movie_data.get('overview', ''))[:9999],  # Truncate to fit field size
                            "tagline": str(movie_data.get('tagline', ''))[:999],  # Truncate to fit field size
                            "release_date": str(movie_data.get('release_date', '')),
                            "revenue": revenue,
                            "status": str(movie_data.get('status', ''))[:49],  # Truncate to fit field size
                            "imdb_rating": imdb_rating,
                            "vote_average": vote_average,
                            "poster_path": str(movie_data.get('poster_path', '')),
                            "genres": genres_json[:999],  # Truncate to fit field size
                            "production_countries": countries_json[:999],  # Truncate to fit field size
                            "vector": embedding_list  # Store the embedding vector
                        }
                        logger.debug(f"Created movie entry for {imdb_id}")
                        batch_data.append(movie_entry)

                    except Exception as e:
                        logger.error(f"Error processing movie {imdb_id}: {str(e)}", exc_info=True)
                        continue

                # Insert batch if we have data
                if batch_data:
                    try:
                        logger.info(f"Attempting to insert batch of {len(batch_data)} movies")
                        self.client.insert(
                            collection_name=self.collection_name,
                            data=batch_data
                        )
                        processed += len(batch_data)
                        logger.info(f"Inserted {processed}/{total_embeddings} movies into collection (failed: {failed})")
                    except Exception as insert_e:
                        logger.error(f"Error inserting batch data: {str(insert_e)}", exc_info=True)
                        failed += len(batch_data)

                # Force garbage collection after each batch
                gc.collect()

            logger.info(f"Successfully inserted {processed}/{total_embeddings} movies into Milvus Lite (failed: {failed})")

            # Ensure vector index exists after inserting data
            if processed > 0 and not self._check_vector_index_exists():
                logger.info("Creating vector index after data insertion...")
                try:
                    self._create_vector_index()
                except Exception as index_e:
                    logger.error(f"Failed to create vector index after data insertion: {str(index_e)}")
                    # Don't fail the entire operation if index creation fails

            return processed > 0

        except Exception as e:
            logger.error(f"Error inserting movie data and embeddings: {str(e)}", exc_info=True)
            return False

    def _build_filter_string(self, min_rating: float, exclude_ids: Optional[Set[str]] = None,
                         preferred_genres: Optional[List[str]] = None) -> str:
        """Build a filter string for Milvus Lite search."""
        # Start with rating filter
        filter_str = f"imdb_rating >= {min_rating}"

        # Add exclude_ids to filter if provided
        if exclude_ids and len(exclude_ids) > 0:
            exclude_list = list(exclude_ids)
            if len(exclude_list) == 1:
                filter_str += f" and imdb_id != '{exclude_list[0]}'"
            else:
                formatted_ids = [f"'{id}'" for id in exclude_list]
                filter_str += f" and imdb_id not in [{', '.join(formatted_ids)}]"

        # Add genre filter if preferred genres are provided
        if preferred_genres and len(preferred_genres) > 0:
            genre_conditions = []
            for genre in preferred_genres:
                genre_conditions.append(f"genres like '%{genre}%'")

            if genre_conditions:
                filter_str += f" and ({' or '.join(genre_conditions)})"

        return filter_str

    def _search_with_vector(self, query_vector: List[float], filter_str: str, limit: int) -> List[Dict[str, Any]]:
        """Perform a vector search in Milvus Lite."""
        if not self._check_initialized():
            return []

        try:
            # Search using the vector field
            search_results = self.client.search(
                collection_name=self.collection_name,
                data=[query_vector],
                filter=filter_str,
                limit=limit,
                output_fields=[
                    "id", "imdb_id", "title", "overview", "tagline",
                    "release_date", "revenue", "status",
                    "imdb_rating", "vote_average", "poster_path",
                    "genres", "production_countries"
                ]
            )

            # Process results
            results = []
            if search_results:
                for hit in search_results[0]:
                    results.append({
                        **hit["entity"],
                        "id": hit["id"],
                        "score": hit.get("distance", 0.0)
                    })

            logger.info(f"Found {len(results)} similar movies")
            return results

        except Exception as e:
            logger.error(f"Error during search: {str(e)}")
            return []

    def search_similar_movies(self,
                             query_embeddings: List[np.ndarray],
                             limit: int = 20,
                             exclude_ids: Optional[Set[str]] = None,
                             min_rating: float = 7.0,
                             preferred_genres: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Search for similar movies based on embeddings in Milvus Lite."""
        if not self._check_initialized():
            return []

        try:
            # Build filter string once
            filter_str = self._build_filter_string(min_rating, exclude_ids, preferred_genres)

            # Process each embedding and collect results
            all_results = []

            for embedding in query_embeddings:
                # Convert embedding to list of floats
                query_vector = self._prepare_embedding(embedding)

                # Search with this vector
                results = self._search_with_vector(query_vector, filter_str, limit)
                all_results.extend(results)

            # Sort by score
            sorted_results = sorted(all_results, key=lambda x: x["score"], reverse=True)

            return sorted_results[:limit]
        except Exception as e:
            logger.error(f"Error searching similar movies: {str(e)}")
            return []

    def search_by_description(self,
                             description_embedding: np.ndarray,
                             limit: int = 20,
                             min_rating: float = 7.0,
                             preferred_genres: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Search for movies based on description embedding in Milvus Lite."""
        if not self._check_initialized():
            return []

        try:
            # Build filter string
            filter_str = self._build_filter_string(min_rating, None, preferred_genres)

            # Convert embedding to list of floats
            query_vector = self._prepare_embedding(description_embedding)

            # Search with this vector
            results = self._search_with_vector(query_vector, filter_str, limit)

            logger.info(f"Performed description-based search with filter: {filter_str}")
            logger.info(f"Found {len(results)} movies matching the description")

            return results

        except Exception as e:
            logger.error(f"Error searching by description: {str(e)}")
            return []

    def get_embeddings_by_imdb_ids(self, imdb_ids: List[str]) -> Dict[str, np.ndarray]:
        """Retrieve embeddings for a list of imdb_ids from Milvus Lite."""
        if not imdb_ids or not self._check_initialized():
            return {}

        try:
            # Build filter expression
            imdb_ids_str = ", ".join([f"'{imdb_id}'" for imdb_id in imdb_ids])
            filter_expr = f"imdb_id in [{imdb_ids_str}]"

            # Query the collection
            query_result = self.client.query(
                collection_name=self.collection_name,
                filter=filter_expr,
                output_fields=["id", "imdb_id", "vector"],
                limit=len(imdb_ids)
            )

            if not query_result:
                logger.warning(f"No embeddings found for the requested imdb_ids")
                return {}

            # Create dictionary mapping imdb_id to embedding vector
            embeddings_dict = {}
            for record in query_result:
                try:
                    imdb_id = record.get('imdb_id')
                    vector = record.get('vector')

                    if imdb_id and vector:
                        embedding = np.array(vector, dtype=np.float32)
                        embeddings_dict[imdb_id] = embedding
                except Exception as e:
                    logger.warning(f"Error processing embedding record: {str(e)}")
                    continue

            logger.info(f"Retrieved {len(embeddings_dict)} embeddings from Milvus Lite")
            return embeddings_dict

        except Exception as e:
            logger.error(f"Error retrieving embeddings from Milvus Lite: {str(e)}")
            return {}

    def drop_collections(self) -> None:
        """Drop the collection in Milvus Lite for clean restart."""
        if not self._check_initialized():
            return

        try:
            if self.check_collections():
                self.client.drop_collection(self.collection_name)
                logger.info(f"Dropped collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} does not exist")

        except Exception as e:
            logger.error(f"Error dropping collection: {str(e)}")

# Create a singleton instance
milvus_service = MilvusService()
