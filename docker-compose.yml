version: '3.8'

services:
  api:
    build: .
    ports:
      - '8000:8000'
    env_file:
      - .env
    depends_on:
      - redis
    volumes:
      - milvus_data:/app/data/milvus:rw
    tmpfs:
      - /tmp:noexec,nosuid,size=1g
    environment:
      - MILVUS_LITE_DISABLE_SOCKET=true
      - MILVUS_LITE_FORCE_FILE_MODE=true
      - TMPDIR=/app/data/milvus

  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data

volumes:
  redis_data:
  milvus_data:
